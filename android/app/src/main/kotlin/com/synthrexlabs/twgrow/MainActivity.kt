package com.synthrexlabs.twgrow

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.synthrexlabs.twgrow/overlay"
    private val OVERLAY_PERMISSION_REQUEST_CODE = 1234
    private var overlayService: OverlayService? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "checkOverlayPermission" -> {
                    val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        Settings.canDrawOverlays(this)
                    } else {
                        true
                    }
                    result.success(hasPermission)
                }
                "requestOverlayPermission" -> {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:$packageName"))
                        startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE)
                        result.success(true)
                    } else {
                        result.success(true)
                    }
                }
                "showOverlay" -> {
                    val current = call.argument<Int>("current") ?: 0
                    val total = call.argument<Int>("total") ?: 0
                    val progressText = call.argument<String>("progressText") ?: "$current of $total channels"
                    val goBackText = call.argument<String>("goBackText") ?: "Go Back to App"
                    val nextChannelText = call.argument<String>("nextChannelText") ?: "Next Channel"
                    showOverlay(current, total, progressText, goBackText, nextChannelText)
                    result.success(true)
                }
                "hideOverlay" -> {
                    hideOverlay()
                    result.success(true)
                }
                "openTwitchChannel" -> {
                    val username = call.argument<String>("username")
                    if (username != null) {
                        try {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.twitch.tv/$username"))
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            startActivity(intent)
                            result.success(true)
                        } catch (e: Exception) {
                            result.error("LAUNCH_ERROR", "Failed to open Twitch channel", e.message)
                        }
                    } else {
                        result.error("INVALID_ARGUMENT", "Username is required", null)
                    }
                }
                "storeVisitedChannel" -> {
                    val username = call.argument<String>("username")
                    if (username != null) {
                        storeVisitedChannel(username)
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGUMENT", "Username is required", null)
                    }
                }
                "getVisitedChannels" -> {
                    val channels = getVisitedChannels()
                    result.success(channels)
                }
                "clearVisitedChannels" -> {
                    clearVisitedChannels()
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun showOverlay(current: Int, total: Int, progressText: String, goBackText: String, nextChannelText: String) {
        if (overlayService == null) {
            overlayService = OverlayService(this)
            // Set method channel for overlay callbacks
            val methodChannel = MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, CHANNEL)
            overlayService?.setMethodChannel(methodChannel)
        }
        overlayService?.showOverlay(current, total, progressText, goBackText, nextChannelText)
    }

    private fun hideOverlay() {
        overlayService?.hideOverlay()
    }

    private fun storeVisitedChannel(username: String) {
        val sharedPrefs = getSharedPreferences("visited_channels", MODE_PRIVATE)
        val visitedChannels = sharedPrefs.getStringSet("channels", mutableSetOf()) ?: mutableSetOf()
        visitedChannels.add(username)
        sharedPrefs.edit().putStringSet("channels", visitedChannels).apply()
    }

    private fun getVisitedChannels(): List<String> {
        val sharedPrefs = getSharedPreferences("visited_channels", MODE_PRIVATE)
        val visitedChannels = sharedPrefs.getStringSet("channels", mutableSetOf()) ?: mutableSetOf()
        return visitedChannels.toList()
    }

    private fun clearVisitedChannels() {
        val sharedPrefs = getSharedPreferences("visited_channels", MODE_PRIVATE)
        sharedPrefs.edit().remove("channels").apply()
    }
}
