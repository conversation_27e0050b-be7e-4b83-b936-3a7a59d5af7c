// ignore_for_file: empty_catches

import 'package:firebase_analytics/firebase_analytics.dart';

class FirebaseAnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static final FirebaseAnalyticsObserver observer = FirebaseAnalyticsObserver(analytics: _analytics);

  // Track Twitch login event
  static Future<void> logTwitchLogin({
    required String userId,
    required String displayName,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'twitch_login',
        parameters: {
          'user_id': userId,
          'display_name': displayName,
          'login_method': 'twitch_oauth',
        },
      );
    } catch (e) {
    }
  }

  // Track campaign creation success event
  static Future<void> logCampaignCreated({
    required String campaignId,
    required String packageName,
    required String targetUsername,
    required int targetFollowers,
    required int coinsSpent,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'campaign_created',
        parameters: {
          'campaign_id': campaignId,
          'package_name': packageName,
          'target_username': targetUsername,
          'target_followers': targetFollowers,
          'coins_spent': coinsSpent,
        },
      );
    } catch (e) {
    }
  }

  // Track purchase event
  static Future<void> logPurchase({
    required String productId,
    required double value,
    required String currency,
    required int coinsReceived,
  }) async {
    try {
      await _analytics.logPurchase(
        currency: currency,
        value: value,
        parameters: {
          'product_id': productId,
          'coins_received': coinsReceived,
          'purchase_type': 'in_app_purchase',
        },
      );
    } catch (e) {
    }
  }

  // Set user properties
  static Future<void> setUserProperties({
    String? userId,
    bool? hasTwitchLogin,
    int? totalCampaigns,
    int? totalPurchases,
  }) async {
    try {
      if (userId != null) {
        await _analytics.setUserId(id: userId);
      }
      
      if (hasTwitchLogin != null) {
        await _analytics.setUserProperty(
          name: 'has_twitch_login',
          value: hasTwitchLogin.toString(),
        );
      }
      
      if (totalCampaigns != null) {
        await _analytics.setUserProperty(
          name: 'total_campaigns',
          value: totalCampaigns.toString(),
        );
      }
      
      if (totalPurchases != null) {
        await _analytics.setUserProperty(
          name: 'total_purchases',
          value: totalPurchases.toString(),
        );
      }
    } catch (e) {
    }
  }
}
