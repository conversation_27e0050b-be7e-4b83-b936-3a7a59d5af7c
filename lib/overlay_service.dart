// ignore_for_file: empty_catches

import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';

class OverlayService {
  static const MethodChannel _channel = MethodChannel('com.synthrexlabs.twgrow/overlay');
  static final OverlayService _instance = OverlayService._internal();

  // Callback functions
  Function(String)? onOverlayAction;

  factory OverlayService() {
    return _instance;
  }

  OverlayService._internal() {
    _channel.setMethodCallHandler(_handleMethodCall);
  }

  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'overlayAction':
        final String action = call.arguments['action'];
        onOverlayAction?.call(action);
        break;
    }
  }

  /// Check if overlay permission is granted
  Future<bool> checkOverlayPermission() async {
    try {
      final bool hasPermission = await _channel.invokeMethod('checkOverlayPermission');
      return hasPermission;
    } catch (e) {
      return false;
    }
  }

  /// Request overlay permission
  Future<bool> requestOverlayPermission() async {
    try {
      final bool result = await _channel.invokeMethod('requestOverlayPermission');
      return result;
    } catch (e) {
      return false;
    }
  }

  /// Show overlay with progress and localized text
  Future<void> showOverlay(int current, int total, {String? progressText, String? goBackText, String? nextChannelText}) async {
    try {
      await _channel.invokeMethod('showOverlay', {
        'current': current,
        'total': total,
        'progressText': progressText ?? '$current of $total channels',
        'goBackText': goBackText ?? 'Go Back to App',
        'nextChannelText': nextChannelText ?? 'Next Channel',
      });
    } catch (e) {
    }
  }

  /// Hide overlay
  Future<void> hideOverlay() async {
    try {
      await _channel.invokeMethod('hideOverlay');
    } catch (e) {
    }
  }

  /// Open Twitch channel
  Future<bool> openTwitchChannel(String username) async {
    try {
      final bool result = await _channel.invokeMethod('openTwitchChannel', {
        'username': username,
      });
      return result;
    } catch (e) {
      return false;
    }
  }

  /// Store visited channel
  Future<void> storeVisitedChannel(String username) async {
    try {
      await _channel.invokeMethod('storeVisitedChannel', {
        'username': username,
      });
    } catch (e) {
    }
  }

  /// Get visited channels
  Future<List<String>> getVisitedChannels() async {
    try {
      final List<dynamic> channels = await _channel.invokeMethod('getVisitedChannels');
      return channels.cast<String>();
    } catch (e) {
      return [];
    }
  }

  /// Clear visited channels
  Future<void> clearVisitedChannels() async {
    try {
      await _channel.invokeMethod('clearVisitedChannels');
    } catch (e) {
    }
  }

  /// Store visited channels in Hive as backup
  Future<void> storeVisitedChannelsInHive(List<String> channels) async {
    try {
      final box = Hive.box("User");
      await box.put("visitedChannels", channels);
    } catch (e) {
    }
  }

  /// Get visited channels from Hive as backup
  Future<List<String>> getVisitedChannelsFromHive() async {
    try {
      final box = Hive.box("User");
      final List<dynamic>? channels = box.get("visitedChannels");
      return channels?.cast<String>() ?? [];
    } catch (e) {
      return [];
    }
  }

  /// Clear visited channels from Hive
  Future<void> clearVisitedChannelsFromHive() async {
    try {
      final box = Hive.box("User");
      await box.delete("visitedChannels");
    } catch (e) {
    }
  }
}
