// ignore_for_file: empty_catches

import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:twitch/provider.dart';
import 'package:twitch/rate.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'buy.dart';
import 'campaign.dart';
import 'follow.dart';
import 'promote.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  DashboardScreenState createState() => DashboardScreenState();
}

class DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const SimplifiedF4FScreen(),
    const CampaignScreen(),
    const PromoteScreen(),
    const BuyScreen(),
  ];
  
  @override
  void initState() {
    final provider = Provider.of<Data>(context, listen: false);
    provider.changeIndex = (int index) {
      setState(() {
        _selectedIndex = index;
      });
    };
    provider.refreshPoints();
    super.initState();
    initPurchases();
    _checkAndShowRatingDialog();
  }

  void _checkAndShowRatingDialog() async {
    final loginBox = Hive.box("User");
    final int appOpenCount = (loginBox.get("appOpenCount") ?? 0) + 1;
    final bool hasRated = loginBox.get("hasRated") ?? false;

    await loginBox.put("appOpenCount", appOpenCount);

    if (!hasRated && appOpenCount % 3 == 0) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => RateAppDialog(
              onRated: () async {
                await loginBox.put("hasRated", true);
              },
            ),
          );
        }
      });
    }
  }

  void initPurchases() async {
    final Data provider = Provider.of(context, listen: false);
    await Purchases.configure(PurchasesConfiguration("goog_SdisEiDUUNnLSWcYKopHRpPulWj"));
    void getOfferings() async {
      try {
        Offerings offerings = await Purchases.getOfferings();
        if (offerings.current != null &&
            offerings.current!.availablePackages.isNotEmpty) {
          List identifiers = [2,10,25,50,100];
          for (var identifier in identifiers) {
            provider.addPackage(offerings.current!.getPackage("$identifier")!);
          }
        }
      } catch (error) {}
    }

    getOfferings();
  }

  final loginBox = Hive.box("User");
  
  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<Data>(context);
    int userCoins = provider.points;
    return Scaffold(
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          'app.title'.tr(),
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedIndex = 3;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF9146FF).withValues(alpha:  0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.monetization_on,
                    size: 20,
                    color: Color(0xFF9146FF),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    userCoins.toString(),
                    style: const TextStyle(
                      color: Color(0xFF9146FF),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      drawer: Drawer(
        backgroundColor: const Color(0xFF0E0E1A),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
              decoration: const BoxDecoration(
                color: Color(0xFF282842),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: _buildDrawerHeader(),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                children: [
                  _buildDrawerItem(
                    icon: Icons.shopping_bag_outlined,
                    title: 'dashboard.buy_coins'.tr(),
                    onTap: () {
                      setState(() {
                        _selectedIndex = 3;
                      });
                      Navigator.pop(context);
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.privacy_tip_outlined,
                    title: 'dashboard.privacy_policy'.tr(),
                    onTap: () {
                      launchUrlString("https://SynthrexLabs.b-cdn.net/twgrow-privacy.html");
                      Navigator.pop(context);
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.description_outlined,
                    title: 'dashboard.eula'.tr(),
                    onTap: () {
                      launchUrlString("https://SynthrexLabs.b-cdn.net/twgrow-eula.html");
                      Navigator.pop(context);
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.star_outline,
                    title: 'dashboard.rate_app'.tr(),
                    onTap: () {
                      Navigator.pop(context);
                      showDialog(
                        context: context,
                        builder: (context) => const RateAppDialog(),
                      ).then((rating) {
                        if (rating != null && context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('rate.thanks_rating'.tr()),
                              backgroundColor: const Color(0xFF282842),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        }
                      });
                    },
                  ),
                  // const Divider(
                  //   color: Color(0xFF282842),
                  //   height: 32,
                  //   thickness: 1,
                  // ),
                  // _buildDrawerItem(
                  //   icon: Icons.logout,
                  //   title: 'dashboard.logout'.tr(),
                  //   textColor: Colors.red,
                  //   iconColor: Colors.red,
                  //   onTap: () {
                  //     provider.resetProvider();
                  //     WebViewCookieManager().clearCookies();
                  //     loginBox.clear();
                  //     Navigator.pop(context);
                  //     Navigator.pushReplacement(
                  //         context,
                  //         MaterialPageRoute(
                  //           builder: (context) => const LoginScreen(),
                  //         ));
                  //   },
                  // ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: _screens,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF282842),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha:0.2),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Theme(
          data: Theme.of(context).copyWith(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
          ),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: const Color(0xFF282842),
            selectedItemColor: const Color(0xFF9146FF),
            unselectedItemColor: Colors.white54,
            showSelectedLabels: true,
            showUnselectedLabels: true,
            selectedLabelStyle: const TextStyle(fontSize: 12),
            unselectedLabelStyle: const TextStyle(fontSize: 12),
            enableFeedback: true,
            landscapeLayout: BottomNavigationBarLandscapeLayout.centered,
            elevation: 0,
            items: [
              BottomNavigationBarItem(
                icon: const Icon(Icons.home),
                label: 'dashboard.channel'.tr(),
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.trending_up),
                label: 'dashboard.campaign'.tr(),
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.campaign),
                label: 'dashboard.promote'.tr(),
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.shopping_bag),
                label: 'dashboard.buy'.tr(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerHeader() {
    // Check if user is logged in with Twitch
    String? token = loginBox.get("token");
    bool isTwitchLoggedIn = token != null && token.isNotEmpty;

    if (isTwitchLoggedIn) {
      // Show user profile for Twitch logged users
      return Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: NetworkImage(loginBox.get("image") ?? "")),
              shape: BoxShape.circle,
              color: const Color(0xFF343450),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 16,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: loginBox.get("image") == null
                ? const Icon(
                    Icons.person,
                    size: 40,
                    color: Color(0xFF9146FF),
                  )
                : null,
          ),
          const SizedBox(height: 16),
          Text(
            loginBox.get("displayname") ?? "",
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      );
    } else {
      // Show app logo and name for non-Twitch users
      return Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
               image: const DecorationImage(
                      image: AssetImage("assets/app-icon.png"),
                      fit: BoxFit.fill,
                    ),
              gradient: const LinearGradient(
                colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF9146FF).withValues(alpha: 0.3),
                  blurRadius: 16,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
          
          ),
          const SizedBox(height: 16),
          const Text(
            'TwGrow',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'dashboard.grow_your_audience'.tr(),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color textColor = Colors.white,
    Color iconColor = Colors.white,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        leading: Icon(
          icon,
          color: iconColor,
          size: 24,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        tileColor: const Color(0xFF282842),
        dense: true,
        visualDensity: const VisualDensity(vertical: -1),
      ),
    );
  }
}